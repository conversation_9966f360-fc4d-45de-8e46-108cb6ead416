Okay, <PERSON>. Here is the code for **PR 5: Server Backend - Core Services (E2, E6 Basics) & Ktor Routes)**. This PR builds upon the foundational data models (PR 1), database schema/transaction scope (PR 2), and core DAOs (PR 4), and Secure Credential Manager (PR 3 Interface).

This PR implements the core business logic for basic session and group management within the Service layer, integrating the DAO interactions using the `TransactionScope` and handling errors using Arrow's `Either`. It also sets up the Ktor routes to expose these Service functions as API endpoints, handling request parsing, service calls, and response formatting, including mapping backend errors to appropriate HTTP status codes.

It includes KDoc documentation for all public components.

* * *

**PR 5: Server Backend - Core Services (E2, E6 Basics) & Ktor Routes**

*   **Assignee:** Alex
*   **Reviewer:** Eric, Maya
*   **Description:** Implement the backend Service layer methods corresponding to basic Session (E2.S1, E2.S3, E2.S4, E6.S1 assign), Group (E6.S3, E6.S4, E6.S6 delete logic), and Credential Status Check (E5.S4) features. These services orchestrate calls to the DAOs, use the `TransactionScope`, and handle errors using Arrow `Either`. Set up the corresponding Ktor API routes (`/api/v1/sessions`, `/api/v1/sessions/{id}`, `/api/v1/sessions/{id}/group`, `/api/v1/groups`, `/api/v1/groups/{id}`, `/api/v1/models/{id}/apikey/status`) in `ApiRoutes.kt` to call these service methods, handling request/response serialization and mapping service results/errors to appropriate HTTP responses.
*   **Stories Addressed:** E2.S1, E2.S3, E2.S4 backend, E6.S1 backend, E6.S3, E6.S4, E6.S6 backend, E5.S4 backend, E1.S4 (LLMClient interface/stub), E7.S3 (Ktor routing setup), E7.S6 (coroutines in services/routes).
*   **Key Files:**
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/SessionService.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/SessionServiceImpl.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/GroupService.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/GroupServiceImpl.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/ModelService.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/ModelServiceImpl.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/MessageService.kt` (Interface placeholder)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/impl/MessageServiceImpl.kt` (Stubbed placeholder)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/external/llm/LLMApiClient.kt` (Interface)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/external/llm/LLMApiClientKtor.kt` (Stubbed implementation for S1)
*   `server/src/main/kotlin/eu/torvian/chatbot/server/api/server/ApiRoutes.kt`
*   `server/src/main/kotlin/eu/torvian/chatbot/server/service/error/*Error.kt` (New Service-level error types)

---

```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/error/ServiceError.kt
package eu.torvian.chatbot.server.service.error

import eu.torvian.chatbot.server.data.dao.error.* // Import DAO error types

/**
 * Sealed interface representing possible domain-specific errors that can occur in the Service layer.
 * These errors might wrap DAO errors or represent business logic failures.
 */
sealed interface ServiceError {
    /**
     * Indicates that a specific entity (Session, Message, Group, Model, Settings, ApiSecret) was not found.
     */
    sealed interface NotFound : ServiceError {
        data class Session(val id: Long) : NotFound, ServiceError
        data class Message(val id: Long) : NotFound, ServiceError
        data class Group(val id: Long) : NotFound, ServiceError
        data class Model(val id: Long) : NotFound, ServiceError
        data class Settings(val id: Long) : NotFound, ServiceError
        data class ApiSecret(val alias: String) : NotFound, ServiceError
    }

    /**
     * Indicates an invalid operation or request data.
     */
    data class InvalidOperation(val message: String) : ServiceError

    /**
     * Indicates a failure related to external communication, such as with the LLM API.
     */
    data class ExternalServiceError(val message: String) : ServiceError

    /**
     * Indicates a configuration issue, such as missing required settings for an operation.
     */
    data class ConfigurationError(val message: String) : ServiceError

    /**
     * Indicates an error during credential management, possibly wrapping a lower-level error.
     */
    data class CredentialError(val message: String) : ServiceError

    /**
     * Represents errors originating from the Data Access Layer (DAO).
     * These wrap specific DAO error types.
     */
    sealed interface DaoError : ServiceError {
        data class Session(val error: SessionError) : DaoError
        data class Message(val error: MessageError) : DaoError
        data class Group(val error: GroupError) : DaoError
        data class Model(val error: ModelError) : DaoError
        data class Settings(val error: SettingsError) : DaoError
        data class ApiSecret(val error: ApiSecretError) : DaoError
    }

    // --- Mapping functions from DAO errors to Service errors ---

    fun SessionError.toServiceError(): DaoError.Session = DaoError.Session(this)
    fun MessageError.toServiceError(): DaoError.Message = DaoError.Message(this)
    fun GroupError.toServiceError(): DaoError.Group = DaoError.Group(this)
    fun ModelError.toServiceError(): DaoError.Model = DaoError.Model(this)
    fun SettingsError.toServiceError(): DaoError.Settings = DaoError.Settings(this)
    fun ApiSecretError.toServiceError(): DaoError.ApiSecret = DaoError.ApiSecret(this)
}
```

```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/SessionService.kt
package eu.torvian.chatbot.server.service

import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatSessionSummary
import eu.torvian.chatbot.server.service.error.ServiceError

/**
 * Service interface for managing chat sessions.
 * Contains core business logic related to sessions, independent of API or data access details.
 */
interface SessionService {
    /**
     * Retrieves summaries for all chat sessions, including group names.
     * @return A list of [ChatSessionSummary] objects. Returns an empty list if no sessions exist.
     */
    suspend fun getAllSessionsSummaries(): List<ChatSessionSummary>

    /**
     * Creates a new chat session.
     * @param name Optional name for the session. If null or blank, a default name may be generated.
     * @return Either a [ServiceError.InvalidOperation] if the request is invalid,
     *         or the newly created [ChatSession].
     */
    suspend fun createSession(name: String?): Either<ServiceError, ChatSession>

    /**
     * Retrieves full details for a specific chat session, including all messages.
     * @param id The ID of the session to retrieve.
     * @return Either a [ServiceError.NotFound.Session] if the session doesn't exist,
     *         or the [ChatSession] object with messages.
     */
    suspend fun getSessionDetails(id: Long): Either<ServiceError.NotFound.Session, ChatSession>

    /**
     * Updates details for a specific chat session.
     * Allows updating name, current model, current settings, and current leaf message ID.
     * @param session The [ChatSession] object with updated fields.
     * @return Either a [ServiceError] or Unit if successful.
     */
    suspend fun updateSessionDetails(session: ChatSession): Either<ServiceError, Unit>

    /**
     * Deletes a chat session and all its messages.
     * @param id The ID of the session to delete.
     * @return Either a [ServiceError.NotFound.Session] if the session doesn't exist, or Unit if successful.
     */
    suspend fun deleteSession(id: Long): Either<ServiceError.NotFound.Session, Unit>

    /**
     * Assigns a chat session to a specific group, or ungroups it.
     * @param id The ID of the session to assign.
     * @param groupId The ID of the target group, or null to ungroup.
     * @return Either a [ServiceError] if the session or group is not found,
     *         or the updated [ChatSessionSummary].
     */
    suspend fun assignSessionToGroup(id: Long, groupId: Long?): Either<ServiceError, ChatSessionSummary>
}
```


```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/GroupService.kt
package eu.torvian.chatbot.server.service

import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatGroup
import eu.torvian.chatbot.server.service.error.ServiceError

/**
 * Service interface for managing chat session groups.
 * Defines business logic for group creation, retrieval, renaming, and deletion.
 */
interface GroupService {
    /**
     * Retrieves a list of all chat groups.
     * @return A list of [ChatGroup] objects. Returns an empty list if no groups exist.
     */
    suspend fun getAllGroups(): List<ChatGroup>

    /**
     * Creates a new chat group.
     * @param name The name for the new group. Must not be blank.
     * @return Either a [ServiceError.InvalidOperation] if the name is blank,
     *         or the newly created [ChatGroup].
     */
    suspend fun createGroup(name: String): Either<ServiceError, ChatGroup>

    /**
     * Renames an existing chat group.
     * @param id The ID of the group to rename.
     * @param newName The new name for the group. Must not be blank.
     * @return Either a [ServiceError] if the group is not found or the new name is invalid,
     *         or Unit if successful.
     */
    suspend fun renameGroup(id: Long, newName: String): Either<ServiceError, Unit>

    /**
     * Deletes a chat group by ID.
     * Sessions previously assigned to this group will become ungrouped.
     * @param id The ID of the group to delete.
     * @return Either a [ServiceError.NotFound.Group] if the group doesn't exist, or Unit if successful.
     */
    suspend fun deleteGroup(id: Long): Either<ServiceError.NotFound.Group, Unit>
}
```



```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/ModelService.kt
package eu.torvian.chatbot.server.service

import arrow.core.Either
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.service.error.ServiceError

/**
 * Service interface for managing LLM Models and their Settings.
 */
interface ModelService {
    /**
     * Retrieves all LLM model configurations.
     */
    suspend fun getAllModels(): List<LLMModel>

    /**
     * Adds a new LLM model configuration.
     * Handles secure storage of the API key if provided.
     * @param name The display name for the model.
     * @param baseUrl The base URL for the LLM API.
     * @param type The type of LLM provider.
     * @param apiKey Optional raw API key string to be stored securely.
     * @return Either a [ServiceError] if model creation or key storage fails,
     *         or the newly created [LLMModel] (without the raw key).
     */
    suspend fun addModel(name: String, baseUrl: String, type: String, apiKey: String?): Either<ServiceError, LLMModel>

    /**
     * Updates an existing LLM model configuration.
     * Handles updating the API key if a new one is provided.
     * @param id The ID of the model to update.
     * @param name New display name (optional).
     * @param baseUrl New base URL (optional).
     * @param type New type (optional).
     * @param apiKey Optional new raw API key string to update the stored key.
     * @return Either a [ServiceError] if the model is not found, update fails, or key update fails,
     *         or the updated [LLMModel] (without the raw key).
     */
    suspend fun updateModel(id: Long, name: String?, baseUrl: String?, type: String?, apiKey: String?): Either<ServiceError, LLMModel>

    /**
     * Deletes an LLM model configuration.
     * Handles deletion of associated settings and the securely stored API key.
     * @param id The ID of the model to delete.
     * @return Either a [ServiceError.NotFound.Model] if the model doesn't exist, or Unit if successful.
     */
    suspend fun deleteModel(id: Long): Either<ServiceError.NotFound.Model, Unit>

    /**
     * Retrieves a specific settings profile by ID.
     * @param id The ID of the settings profile.
     * @return Either a [ServiceError.NotFound.Settings] if not found, or the [ModelSettings].
     */
    suspend fun getSettingsById(id: Long): Either<ServiceError.NotFound.Settings, ModelSettings>

    /**
     * Retrieves all settings profiles stored in the database.
     * @return A list of all [ModelSettings] objects.
     */
    suspend fun getAllSettings(): List<ModelSettings> // Non-suspend if just delegates to suspend DAO? Let's keep suspend for consistency

    /**
     * Retrieves all settings profiles associated with a specific LLM model.
     * @param modelId The ID of the LLM model.
     * @return A list of [ModelSettings] for the model, or an empty list if none exist.
     */
    suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings> // Keep suspend

    /**
     * Adds a new settings profile for a model.
     * @param modelId The ID of the associated model.
     * @param name The name of the settings profile.
     * @param systemMessage System message (optional).
     * @param temperature Temperature (optional).
     * @param maxTokens Max tokens (optional).
     * @param customParamsJson Custom params JSON (optional).
     * @return Either a [ServiceError] if the model is not found or insertion fails,
     *         or the newly created [ModelSettings].
     */
    suspend fun addSettings(
        modelId: Long, name: String, systemMessage: String?, temperature: Float?,
        maxTokens: Int?, customParamsJson: String?
    ): Either<ServiceError, ModelSettings>

    /**
     * Updates an existing settings profile.
     * @param id The ID of the settings profile to update.
     * @param name New name (optional).
     * @param systemMessage New system message (optional).
     * @param temperature New temperature (optional).
     * @param maxTokens New max tokens (optional).
     * @param customParamsJson New custom params JSON (optional).
     * @return Either a [ServiceError] if not found or update fails, or Unit if successful.
     */
    suspend fun updateSettings(
        id: Long, name: String?, systemMessage: String?, temperature: Float?,
        maxTokens: Int?, customParamsJson: String?
    ): Either<ServiceError, Unit>

    /**
     * Deletes a settings profile.
     * @param id The ID of the settings profile to delete.
     * @return Either a [ServiceError.NotFound.Settings] if not found, or Unit if successful.
     */
    suspend fun deleteSettings(id: Long): Either<ServiceError.NotFound.Settings, Unit>
}
```



```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/service/MessageService.kt
package eu.torvian.chatbot.server.service

import arrow.core.Either
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.server.service.error.ServiceError

/**
 * Service interface for managing Chat Messages and their threading relationships.
 * Contains core business logic for message processing and modification.
 */
interface MessageService {

    /**
     * Retrieves a list of all messages for a specific session, ordered by creation time.
     * @param sessionId The ID of the session.
     * @return A list of [ChatMessage] objects.
     */
    suspend fun getMessagesBySessionId(sessionId: Long): List<ChatMessage> // List doesn't use Either, assume session exists (checked by SessionService)

    /**
     * Processes a new incoming user message (including replies).
     *
     * Orchestrates saving the user message, building LLM context (thread-aware),
     * calling the LLM (stubbed in V1.1), saving the assistant message, and
     * updating thread relationships in the database.
     *
     * @param sessionId The ID of the session the message belongs to.
     * @param content The user's message content.
     * @param parentMessageId Optional ID of the message being replied to (null for root messages).
     * @return Either a [ServiceError] if processing fails (e.g., session/parent not found, LLM config error, LLM API error),
     *         or a list containing the newly created user and assistant messages ([userMsg, assistantMsg]).
     */
    suspend fun processNewMessage(sessionId: Long, content: String, parentMessageId: Long? = null): Either<ServiceError, List<ChatMessage>>

    /**
     * Updates the content of an existing message.
     * @param id The ID of the message to update.
     * @param content The new content.
     * @return Either a [ServiceError.NotFound.Message] if the message doesn't exist,
     *         or the updated [ChatMessage].
     */
    suspend fun updateMessageContent(id: Long, content: String): Either<ServiceError.NotFound.Message, ChatMessage>

    /**
     * Deletes a specific message and its children recursively.
     * Updates the parent's children list.
     * @param id The ID of the message to delete.
     * @return Either a [ServiceError.NotFound.Message] if the message doesn't exist, or Unit if successful.
     */
    suspend fun deleteMessage(id: Long): Either<ServiceError.NotFound.Message, Unit>

    /**
     * Checks if an API key is configured for a specific model.
     * Included here based on initial API/Service structure, but ideally resides in ModelService.
     * Moving this to ModelService in a later refactor is advisable.
     * @param modelId The ID of the model.
     * @return True if an API key ID is stored for the model, false otherwise.
     */
    suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean // Does not return Either, simple boolean check
}
```


```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/external/llm/LLMApiClient.kt
package eu.torvian.chatbot.server.external.llm

import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.external.models.OpenAiApiModels.* // Use external DTOs

/**
 * Interface for interacting with external LLM APIs (OpenAI-compatible).
 *
 * All methods are suspending as they involve network I/O.
 */
interface LLMApiClient {
    /**
     * Sends a chat completion request to the LLM API.
     *
     * @param messages The list of messages forming the conversation context (thread-aware, built by Service).
     * @param modelConfig Configuration details for the target LLM endpoint.
     * @param settings Specific settings profile to use for this completion request.
     * @param apiKey The decrypted API key for authentication.
     * @return The response from the LLM API, including the assistant's message.
     * @throws Exception If the API call fails (e.g., network error, API error response).
     */
    suspend fun completeChat(
        messages: List<ChatMessage>, // History + current user message. Service builds this list considering threads.
        modelConfig: LLMModel,      // Base URL, Type (for endpoint structure)
        settings: ModelSettings,    // Temperature, System Message, etc.
        apiKey: String              // The _decrypted_ API key
    ): ChatCompletionResponse // Using a DTO from external.models
}
```





```kotlin
// file: server/src/main/kotlin/eu/torvian/chatbot/server/api/server/ApiRoutes.kt
package eu.torvian.chatbot.server.api.server

import arrow.core.Either
import eu.torvian.chatbot.common.models.* // Common DTOs
import eu.torvian.chatbot.server.service.* // Service Interfaces
import eu.torvian.chatbot.server.service.error.ServiceError // Service error types
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import java.util.logging.Level
import java.util.logging.Logger

private val logger = Logger.getLogger("ApiRoutes")

/**
 * Configures the Ktor server routing for the application API (v1).
 *
 * @param sessionService The injected SessionService instance.
 * @param messageService The injected MessageService instance.
 * @param modelService The injected ModelService instance.
 * @param groupService The injected GroupService instance.
 */
fun Application.configureRouting(
    sessionService: SessionService,
    messageService: MessageService, // Use MessageService interface
    modelService: ModelService,
    groupService: GroupService
) {
    routing {
        route("/api/v1") {

            // --- Session Routes (/sessions) ---
            route("/sessions") {
                // GET /api/v1/sessions (E2.S3)
                get {
                    try {
                        val sessions = sessionService.getAllSessionsSummaries() // Calls suspend service
                        call.respond(HttpStatusCode.OK, sessions)
                    } catch (e: Exception) { // Catch unexpected exceptions
                        logger.log(Level.SEVERE, "Error getting sessions", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve sessions: ${e.message}")
                    }
                }

                // POST /api/v1/sessions (E2.S1)
                post {
                    try {
                        val request = call.receive<CreateSessionRequest>()
                        sessionService.createSession(request.name) // Calls suspend service
                            .fold( // Handle Either result
                                { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError to HTTP response
                                { session -> call.respond(HttpStatusCode.Created, session) } // Respond with session on success
                            )
                    } catch (e: Exception) { // Catch unexpected exceptions (e.g., Ktor receive exception)
                        logger.log(Level.SEVERE, "Error receiving or processing create session request", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to process create session request: ${e.message}")
                    }
                }

                route("/{sessionId}") {
                    // GET /api/v1/sessions/{sessionId} (E2.S4)
                    get {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull()
                            ?: return@get call.respond(HttpStatusCode.BadRequest, "Invalid session ID format")
                        try {
                             sessionService.getSessionDetails(sessionId) // Calls suspend service
                                 .fold( // Handle Either result
                                     { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                     { session -> call.respond(HttpStatusCode.OK, session) } // Respond with session
                                 )
                        } catch (e: Exception) { // Catch unexpected exceptions
                             logger.log(Level.SEVERE, "Error getting session $sessionId details", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve session details: ${e.message}")
                        }
                    }

                    // PUT /api/v1/sessions/{sessionId} (E2.S5, E4.S7, E1.S4 leaf update)
                    put {
                         val sessionId = call.parameters["sessionId"]?.toLongOrNull()
                             ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID format")
                         try {
                             val request = call.receive<UpdateSessionRequest>() // Use UpdateSessionRequest DTO
                             // Need to fetch session, apply updates from request, and call service.updateSessionDetails
                             // This endpoint's full implementation will come later.
                             call.respond(HttpStatusCode.NotImplemented) // Placeholder
                         } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error processing update session $sessionId request", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to process update session request: ${e.message}")
                         }
                    }

                    // DELETE /api/v1/sessions/{sessionId} (E2.S6)
                    delete {
                         val sessionId = call.parameters["sessionId"]?.toLongOrNull()
                             ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid session ID format")
                         try {
                              sessionService.deleteSession(sessionId) // Calls suspend service
                                  .fold( // Handle Either result
                                      { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                      { call.respond(HttpStatusCode.NoContent) } // 204 No Content on success
                                  )
                         }
                         catch (e: Exception) { // Catch unexpected exceptions
                              logger.log(Level.SEVERE, "Error deleting session $sessionId", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to delete session: ${e.message}")
                         }
                    }

                    // PUT /api/v1/sessions/{sessionId}/group (E6.S1)
                    put("/group") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull()
                            ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID format")
                        try {
                            val request = call.receive<AssignSessionToGroupRequest>() // Request body contains groupId (nullable)
                            sessionService.assignSessionToGroup(sessionId, request.groupId) // Calls suspend service
                                .fold( // Handle Either result
                                     { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                     { updatedSummary -> call.respond(HttpStatusCode.OK, updatedSummary) } // Respond with updated summary
                                )
                        } catch (e: Exception) { // Catch unexpected exceptions
                            logger.log(Level.SEVERE, "Error assigning session $sessionId to group", e)
                           call.respond(HttpStatusCode.InternalServerError, "Failed to assign session to group: ${e.message}")
                        }
                    }
                }
            } // End /sessions routes

            // --- Message Routes (nested under sessions or separate) ---
            // Defined under session for clarity in OpenAPI, but logic operates on messageId
            route("/sessions/{sessionId}/messages") {
                 // POST /api/v1/sessions/{sessionId}/messages (E1.S1 backend, E1.S4 Sprint 1 scope)
                 post {
                     val sessionId = call.parameters["sessionId"]?.toLongOrNull()
                         ?: return@post call.respond(HttpStatusCode.BadRequest, "Invalid session ID format")
                     try {
                         val request = call.receive<ProcessNewMessageRequest>()
                         messageService.processNewMessage(sessionId, request.content, request.parentMessageId) // Calls suspend message service
                             .fold( // Handle Either result
                                 { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError (includes LLM errors)
                                 { messages -> call.respond(HttpStatusCode.OK, messages) } // Returns list [userMsg, assistantMsg]
                             )
                     } catch (e: Exception) { // Catch unexpected exceptions
                         logger.log(Level.SEVERE, "Error processing new message in session $sessionId", e)
                         call.respond(HttpStatusCode.InternalServerError, e.message ?: "Failed to process message")
                     }
                 }
            } // End /sessions/{sessionId}/messages route

            // --- Message Management Routes (/messages) ---
            route("/messages") {
                 route("/{messageId}") {
                     // PUT /api/v1/messages/{messageId} (E3.S3)
                     put {
                         val messageId = call.parameters["messageId"]?.toLongOrNull()
                            ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid message ID format")
                         try {
                             val request = call.receive<UpdateMessageRequest>() // Use UpdateMessageRequest DTO
                             messageService.updateMessageContent(messageId, request.content) // Calls suspend message service
                                 .fold( // Handle Either result
                                     { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                     { message -> call.respond(HttpStatusCode.OK, message) } // Respond with updated message
                                 )
                         } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error processing update message $messageId request", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to process update message request: ${e.message}")
                         }
                     }

                     // DELETE /api/v1/messages/{messageId} (E3.S4)
                     delete {
                          val messageId = call.parameters["messageId"]?.toLongOrNull()
                            ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid message ID format")
                          try {
                              messageService.deleteMessage(messageId) // Calls suspend message service (recursive delete)
                                  .fold( // Handle Either result
                                      { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                      { call.respond(HttpStatusCode.NoContent) } // 204 No Content on successful deletion
                                  )
                          }
                          catch (e: Exception) { // Catch unexpected exceptions
                              logger.log(Level.SEVERE, "Error deleting message $messageId", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to delete message: ${e.message}")
                          }
                     }
                 }
            } // End /messages routes


            // --- Model Routes (/models) ---
            route("/models") {
                // GET /api/v1/models (E4.S2)
                get {
                    try {
                        val models = modelService.getAllModels() // Calls suspend service
                        call.respond(HttpStatusCode.OK, models)
                    } catch (e: Exception) {
                        logger.log(Level.SEVERE, "Error getting models", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve models: ${e.message}")
                    }
                }

                // POST /api/v1/models (E4.S1 backend - part involves secure key storage)
                post {
                    try {
                         val request = call.receive<AddModelRequest>() // Use AddModelRequest DTO
                         modelService.addModel(request.name, request.baseUrl, request.type, request.apiKey) // Calls suspend service
                              .fold( // Handle Either result
                                  { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError (includes credential errors)
                                  { model -> call.respond(HttpStatusCode.Created, model) } // Respond with model (without raw key)
                              )
                    } catch (e: Exception) { // Catch unexpected exceptions
                        logger.log(Level.SEVERE, "Error processing add model request", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to process add model request: ${e.message}")
                    }
                }

                route("/{modelId}") {
                    // PUT /api/v1/models/{modelId} (E4.S3 backend - part involves secure key update)
                    put {
                         val modelId = call.parameters["modelId"]?.toLongOrNull()
                            ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid model ID format")
                         try {
                             val request = call.receive<UpdateModelRequest>() // Use UpdateModelRequest DTO
                             // Ensure ID in request body matches path param for safety/consistency
                             if (request.id != modelId) {
                                 return@put call.respond(HttpStatusCode.BadRequest, "Model ID in path and body must match")
                             }
                             modelService.updateModel(request.id, request.name, request.baseUrl, request.type, request.apiKey) // Calls suspend service
                                  .fold( // Handle Either result
                                      { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                      { model -> call.respond(HttpStatusCode.OK, model) } // Respond with updated model
                                  )
                         } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error processing update model $modelId request", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to process update model request: ${e.message}")
                         }
                    }
                    // DELETE /api/v1/models/{modelId} (E4.S4 backend - part involves secure key deletion)
                    delete {
                         val modelId = call.parameters["modelId"]?.toLongOrNull()
                            ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid model ID format")
                         try {
                              modelService.deleteModel(modelId) // Calls suspend service
                                  .fold( // Handle Either result
                                      { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError (includes credential errors)
                                      { call.respond(HttpStatusCode.NoContent) } // 204 No Content
                                  )
                         }
                         catch (e: Exception) { // Catch unexpected exceptions
                              logger.log(Level.SEVERE, "Error deleting model $modelId", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to delete model: ${e.message}")
                         }
                    }

                    // GET /api/v1/models/{modelId}/apikey/status (E5.S4)
                    get("/apikey/status") {
                        val modelId = call.parameters["modelId"]?.toLongOrNull()
                            ?: return@get call.respond(HttpStatusCode.BadRequest, "Invalid model ID format")
                        try {
                            // Note: isApiKeyConfiguredForModel is in MessageService in architecture, but logically ModelService is better.
                            // Using messageService as per current architecture definition for V1.1, but flagging this for refactor.
                            val isConfigured = messageService.isApiKeyConfiguredForModel(modelId) // Calls suspend service
                            call.respond(HttpStatusCode.OK, ApiKeyStatusResponse(isConfigured))
                        } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error checking API key status for model $modelId", e)
                           call.respond(HttpStatusCode.InternalServerError, "Failed to check API key status: ${e.message}")
                        }
                    }
                }
            } // End /models routes

            // --- Settings Routes (/settings) ---
            route("/settings") {
                 route("/{settingsId}") {
                     // GET /api/v1/settings/{settingsId}
                     get {
                         val settingsId = call.parameters["settingsId"]?.toLongOrNull()
                            ?: return@get call.respond(HttpStatusCode.BadRequest, "Invalid settings ID format")
                         try {
                              modelService.getSettingsById(settingsId) // Calls suspend service
                                   .fold( // Handle Either result
                                       { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                       { settings -> call.respond(HttpStatusCode.OK, settings) } // Respond with settings
                                   )
                         } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error getting settings $settingsId details", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve settings details: ${e.message}")
                         }
                     }
                     // PUT /api/v1/settings/{settingsId} (E4.S6)
                     put {
                         val settingsId = call.parameters["settingsId"]?.toLongOrNull()
                            ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid settings ID format")
                         try {
                             val request = call.receive<UpdateSettingsRequest>() // Use UpdateSettingsRequest DTO
                             // Ensure ID in request body matches path param
                              if (request.id != settingsId) {
                                  return@put call.respond(HttpStatusCode.BadRequest, "Settings ID in path and body must match")
                              }
                             modelService.updateSettings(request.id, request.name, request.systemMessage, request.temperature, request.maxTokens, request.customParamsJson) // Calls suspend service
                                  .fold( // Handle Either result
                                      { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                      { call.respond(HttpStatusCode.OK) } // Respond with 200 OK on success (no body for PUT update)
                                  )
                         } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error processing update settings $settingsId request", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to process update settings request: ${e.message}")
                         }
                     }
                     // DELETE /api/v1/settings/{settingsId} (E4.S5)
                     delete {
                         val settingsId = call.parameters["settingsId"]?.toLongOrNull()
                            ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid settings ID format")
                         try {
                              modelService.deleteSettings(settingsId) // Calls suspend service
                                  .fold( // Handle Either result
                                      { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                      { call.respond(HttpStatusCode.NoContent) } // 204 No Content
                                  )
                         }
                         catch (e: Exception) { // Catch unexpected exceptions
                              logger.log(Level.SEVERE, "Error deleting settings $settingsId", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to delete settings: ${e.message}")
                         }
                     }
                 }
             } // End /settings routes

             // --- Settings under Models Routes ---
             route("/models/{modelId}/settings") {
                 // POST /api/v1/models/{modelId}/settings (E4.S5)
                 post {
                      val modelId = call.parameters["modelId"]?.toLongOrNull()
                         ?: return@post call.respond(HttpStatusCode.BadRequest, "Invalid model ID format")
                      try {
                          val request = call.receive<AddModelSettingsRequest>() // Use AddModelSettingsRequest DTO
                          modelService.addSettings(modelId, request.name, request.systemMessage, request.temperature, request.maxTokens, request.customParamsJson) // Calls suspend service
                              .fold( // Handle Either result
                                  { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                  { settings -> call.respond(HttpStatusCode.Created, settings) } // Respond with new settings
                              )
                      } catch (e: Exception) {
                         logger.log(Level.SEVERE, "Error processing add settings for model $modelId request", e)
                         call.respond(HttpStatusCode.InternalServerError, "Failed to process add settings request: ${e.message}")
                     }
                 }
             } // End settings under models


            // --- Group Routes (/groups) ---
            route("/groups") {
                // GET /api/v1/groups (E6.S4)
                get {
                    try {
                        val groups = groupService.getAllGroups() // Calls suspend service
                        call.respond(HttpStatusCode.OK, groups)
                    } catch (e: Exception) { // Catch unexpected exceptions
                        logger.log(Level.SEVERE, "Error getting groups", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to retrieve groups: ${e.message}")
                    }
                }

                // POST /api/v1/groups (E6.S3)
                post {
                    try {
                        val request = call.receive<CreateGroupRequest>()
                        groupService.createGroup(request.name) // Calls suspend service
                            .fold( // Handle Either result
                                { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                { group -> call.respond(HttpStatusCode.Created, group) } // Respond with new group
                            )
                    } catch (e: Exception) { // Catch unexpected exceptions
                        logger.log(Level.SEVERE, "Error processing create group request", e)
                        call.respond(HttpStatusCode.InternalServerError, "Failed to process create group request: ${e.message}")
                    }
                }

                route("/{groupId}") {
                    // PUT /api/v1/groups/{groupId} (E6.S5)
                    put {
                        val groupId = call.parameters["groupId"]?.toLongOrNull()
                            ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid group ID format")
                         try {
                             val request = call.receive<RenameGroupRequest>() // Use RenameGroupRequest DTO
                             groupService.renameGroup(groupId, request.name) // Calls suspend service
                                  .fold( // Handle Either result
                                       { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                       { call.respond(HttpStatusCode.OK) } // Respond with 200 OK on success (no body for PUT update)
                                  )
                         } catch (e: Exception) {
                            logger.log(Level.SEVERE, "Error processing rename group $groupId request", e)
                             call.respond(HttpStatusCode.InternalServerError, "Failed to process rename group request: ${e.message}")
                         }
                    }

                    // DELETE /api/v1/groups/{groupId} (E6.S6 backend - handles session ungrouping)
                    delete {
                        val groupId = call.parameters["groupId"]?.toLongOrNull()
                            ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid group ID format")
                        try {
                             groupService.deleteGroup(groupId) // Calls suspend service
                                 .fold( // Handle Either result
                                     { error -> mapServiceErrorToHttpResponse(error, call) }, // Map ServiceError
                                     { call.respond(HttpStatusCode.NoContent) } // 204 No Content
                                 )
                        }
                        catch (e: Exception) { // Catch unexpected exceptions
                            logger.log(Level.SEVERE, "Error deleting group $groupId", e)
                           call.respond(HttpStatusCode.InternalServerError, "Failed to delete group: ${e.message}")
                        }
                    }
                }
            } // End /groups routes

        } // End /api/v1 routes
    }
}

/**
 * Helper function to map a [ServiceError] to an appropriate [HttpStatusCode] and response body.
 *
 * @param error The service error to map.
 * @param call The Ktor [ApplicationCall] to respond to.
 */
private suspend fun mapServiceErrorToHttpResponse(error: ServiceError, call: ApplicationCall) {
    logger.log(Level.WARNING, "Mapping service error to HTTP response: $error")
    when (error) {
        is ServiceError.NotFound -> {
            val entityType = when(error) {
                is ServiceError.NotFound.Session -> "Session"
                is ServiceError.NotFound.Message -> "Message"
                is ServiceError.NotFound.Group -> "Group"
                is ServiceError.NotFound.Model -> "Model"
                is ServiceError.NotFound.Settings -> "Settings"
                is ServiceError.NotFound.ApiSecret -> "Api Secret"
            }
            val entityId = when(error) {
                is ServiceError.NotFound.Session -> error.id
                is ServiceError.NotFound.Message -> error.id
                is ServiceError.NotFound.Group -> error.id
                is ServiceError.NotFound.Model -> error.id
                is ServiceError.NotFound.Settings -> error.id
                is ServiceError.NotFound.ApiSecret -> error.alias
            }
            call.respond(HttpStatusCode.NotFound, "$entityType with ID $entityId not found.")
        }
        is ServiceError.InvalidOperation -> call.respond(HttpStatusCode.BadRequest, error.message)
        is ServiceError.ConfigurationError -> call.respond(HttpStatusCode.BadRequest, error.message) // 400 for client-side config issue
        is ServiceError.CredentialError -> call.respond(HttpStatusCode.InternalServerError, "Credential error: ${error.message}") // 500 for server-side credential issue
        is ServiceError.ExternalServiceError -> call.respond(HttpStatusCode.InternalServerError, "External service error: ${error.message}") // 500 for LLM API error etc.
        is ServiceError.DaoError -> {
            // Map specific DAO errors if needed, otherwise default to 500
            val errorMessage = when(error.error) {
                 is SessionError.ForeignKeyViolation -> "Related entity not found for session operation"
                 is MessageError.ForeignKeyViolation -> "Related entity not found for message operation"
                 else -> "Database error" // Generic for other DAO errors not explicitly handled
            }
            // For simplicity in V1.1, map most DAO errors to 500, specific FkViolation might be 400/404
            // Let's map ForeignKeyViolation to 400, others to 500 for now.
            when (error.error) {
                 is SessionError.ForeignKeyViolation, is MessageError.ForeignKeyViolation -> call.respond(HttpStatusCode.BadRequest, errorMessage)
                 else -> call.respond(HttpStatusCode.InternalServerError, "Database error: ${error.error}") // Avoid exposing raw DB error messages
            }

        }
    }
}
```

This concludes the code for PR 5. Alex will implement these Services, update the Ktor routes, ensure correct DI wiring with the DAOs (from PR 4), TransactionScope (from PR 2), LLMClient/CredentialManager (from PR 5 interfaces, implementations in PR 5 stub and PR 3), and submit the PR.